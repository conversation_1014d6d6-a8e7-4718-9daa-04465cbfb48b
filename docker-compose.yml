services:  
  production:
    # image: ghcr.io/presenton/presenton:latest
    build:
      context: .
      dockerfile: Dockerfile
    ports:  
      # You can replace 5000 with any other port number of your choice to run Presenton on a different port number.
      - "5000:80"
    volumes:  
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - DATABASE_URL=${DATABASE_URL}

  production-gpu:
    # image: ghcr.io/presenton/presenton:latest
    build:
      context: .
      dockerfile: Dockerfile
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    ports:  
      # You can replace 5000 with any other port number of your choice to run Presenton on a different port number.
      - "5000:80"
    volumes:  
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - DATABASE_URL=${DATABASE_URL}

  development:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5000:80" 
    volumes:
      - .:/app
      - ./app_data:/app_data
    environment:
      - NODE_ENV=development
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - DATABASE_URL=${DATABASE_URL}

  development-gpu:
    build:
      context: .
      dockerfile: Dockerfile.dev
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    ports:
      - "5000:80" 
    volumes:
      - .:/app
      - ./app_data:/app_data
    environment:
      - NODE_ENV=development
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - DATABASE_URL=${DATABASE_URL}