FROM python:3.11-slim-bookworm

# Install Node.js and npm
RUN apt-get update && apt-get install -y \
  nodejs \  
  npm \
  nginx \
  curl \
  redis-server

# Change working directory
WORKDIR /app

RUN ls -a

# Set environment variables
ENV APP_DATA_DIRECTORY=/app_data
ENV TEMP_DIRECTORY=/tmp/presenton

# Install ollama
RUN curl -fsSL https://ollama.com/install.sh | sh

# Install dependencies for FastAPI
COPY servers/fastapi/requirements.txt ./
RUN pip install -r requirements.txt

# Install dependencies for Next.js
WORKDIR /node_dependencies
COPY servers/nextjs/package.json servers/nextjs/package-lock.json ./
RUN npm install 

# Install chrome for puppeteer
RUN npx puppeteer browsers install chrome@138.0.7204.94 --install-deps

RUN chmod -R 777 /node_dependencies

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose the port
E<PERSON><PERSON>SE 80

# Start the servers
CMD ["/bin/bash", "/app/docker-dev-start.sh"]