.SwitchRoot {
  width: 34px;
  height: 18px;
  background-color: white;
  border-radius: 9999px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 137, 0.549);
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  /* &:focus {
    box-shadow: 0 0 0 2px black;
  } */

  /* &[data-state="checked"] {
    background-color: rgb(214, 215, 255);
  } */
}

.SwitchThumb {
  display: block;
  width: 16px;
  height: 16px;
  background-color: blueviolet;
  border-radius: 9999px;
  transition: transform 100ms;
  transform: translateX(2px);
  will-change: transform;

  &[data-state="checked"] {
    transform: translateX(16px);
  }
}
