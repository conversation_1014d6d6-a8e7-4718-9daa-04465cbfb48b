export enum PptxBoxShapeEnum {
  RECTANGLE = "rectangle",
  CIRCLE = "circle"
}

export enum PptxObjectFitEnum {
  CONTAIN = "contain",
  COVER = "cover",
  FILL = "fill"
}

export enum PptxAlignment {
  CENTER = 2,
  DISTRIBUTE = 5,
  JUSTIFY = 4,
  JUSTIFY_LOW = 7,
  LEFT = 1,
  RIGHT = 3,
  THAI_DISTRIBUTE = 6,
  MIXED = -2
}

export enum PptxShapeType {
  ACTION_BUTTON_BACK_OR_PREVIOUS = 129,
  ACTION_BUTTON_BEGINNING = 131,
  ACTION_BUTTON_CUSTOM = 125,
  ACTION_BUTTON_DOCUMENT = 134,
  ACTION_BUTTON_END = 132,
  ACTION_BUTTON_FORWARD_OR_NEXT = 130,
  ACTION_BUTTON_HELP = 127,
  ACTION_BUTTON_HOME = 126,
  ACTION_BUTTON_INFORMATION = 128,
  ACTION_BUTTON_MOVIE = 136,
  ACTION_BUTTON_RETURN = 133,
  ACTION_BUTTON_SOUND = 135,
  ARC = 25,
  BALLOON = 137,
  BENT_ARROW = 41,
  BENT_UP_ARROW = 44,
  BEVEL = 15,
  BLOCK_ARC = 20,
  CAN = 13,
  CHART_PLUS = 182,
  CHART_STAR = 181,
  CHART_X = 180,
  CHEVRON = 52,
  CHORD = 161,
  CIRCULAR_ARROW = 60,
  CLOUD = 179,
  CLOUD_CALLOUT = 108,
  CORNER = 162,
  CORNER_TABS = 169,
  CROSS = 11,
  CUBE = 14,
  CURVED_DOWN_ARROW = 48,
  CURVED_DOWN_RIBBON = 100,
  CURVED_LEFT_ARROW = 46,
  CURVED_RIGHT_ARROW = 45,
  CURVED_UP_ARROW = 47,
  CURVED_UP_RIBBON = 99,
  DECAGON = 144,
  DIAGONAL_STRIPE = 141,
  DIAMOND = 4,
  DODECAGON = 146,
  DONUT = 18,
  DOUBLE_BRACE = 27,
  DOUBLE_BRACKET = 26,
  DOUBLE_WAVE = 104,
  DOWN_ARROW = 36,
  DOWN_ARROW_CALLOUT = 56,
  DOWN_RIBBON = 98,
  EXPLOSION1 = 89,
  EXPLOSION2 = 90,
  FLOWCHART_ALTERNATE_PROCESS = 62,
  FLOWCHART_CARD = 75,
  FLOWCHART_COLLATE = 79,
  FLOWCHART_CONNECTOR = 73,
  FLOWCHART_DATA = 64,
  FLOWCHART_DECISION = 63,
  FLOWCHART_DELAY = 84,
  FLOWCHART_DIRECT_ACCESS_STORAGE = 87,
  FLOWCHART_DISPLAY = 88,
  FLOWCHART_DOCUMENT = 67,
  FLOWCHART_EXTRACT = 81,
  FLOWCHART_INTERNAL_STORAGE = 66,
  FLOWCHART_MAGNETIC_DISK = 86,
  FLOWCHART_MANUAL_INPUT = 71,
  FLOWCHART_MANUAL_OPERATION = 72,
  FLOWCHART_MERGE = 82,
  FLOWCHART_MULTIDOCUMENT = 68,
  FLOWCHART_OFFLINE_STORAGE = 139,
  FLOWCHART_OFFPAGE_CONNECTOR = 74,
  FLOWCHART_OR = 78,
  FLOWCHART_PREDEFINED_PROCESS = 65,
  FLOWCHART_PREPARATION = 70,
  FLOWCHART_PROCESS = 61,
  FLOWCHART_PUNCHED_TAPE = 76,
  FLOWCHART_SEQUENTIAL_ACCESS_STORAGE = 85,
  FLOWCHART_SORT = 80,
  FLOWCHART_STORED_DATA = 83,
  FLOWCHART_SUMMING_JUNCTION = 77,
  FLOWCHART_TERMINATOR = 69,
  FOLDED_CORNER = 16,
  FRAME = 158,
  FUNNEL = 174,
  GEAR_6 = 172,
  GEAR_9 = 173,
  HALF_FRAME = 159,
  HEART = 21,
  HEPTAGON = 145,
  HEXAGON = 10,
  HORIZONTAL_SCROLL = 102,
  ISOSCELES_TRIANGLE = 7,
  LEFT_ARROW = 34,
  LEFT_ARROW_CALLOUT = 54,
  LEFT_BRACE = 31,
  LEFT_BRACKET = 29,
  LEFT_CIRCULAR_ARROW = 176,
  LEFT_RIGHT_ARROW = 37,
  LEFT_RIGHT_ARROW_CALLOUT = 57,
  LEFT_RIGHT_CIRCULAR_ARROW = 177,
  LEFT_RIGHT_RIBBON = 140,
  LEFT_RIGHT_UP_ARROW = 40,
  LEFT_UP_ARROW = 43,
  LIGHTNING_BOLT = 22,
  LINE_CALLOUT_1 = 109,
  LINE_CALLOUT_1_ACCENT_BAR = 113,
  LINE_CALLOUT_1_BORDER_AND_ACCENT_BAR = 121,
  LINE_CALLOUT_1_NO_BORDER = 117,
  LINE_CALLOUT_2 = 110,
  LINE_CALLOUT_2_ACCENT_BAR = 114,
  LINE_CALLOUT_2_BORDER_AND_ACCENT_BAR = 122,
  LINE_CALLOUT_2_NO_BORDER = 118,
  LINE_CALLOUT_3 = 111,
  LINE_CALLOUT_3_ACCENT_BAR = 115,
  LINE_CALLOUT_3_BORDER_AND_ACCENT_BAR = 123,
  LINE_CALLOUT_3_NO_BORDER = 119,
  LINE_CALLOUT_4 = 112,
  LINE_CALLOUT_4_ACCENT_BAR = 116,
  LINE_CALLOUT_4_BORDER_AND_ACCENT_BAR = 124,
  LINE_CALLOUT_4_NO_BORDER = 120,
  LINE_INVERSE = 183,
  MATH_DIVIDE = 166,
  MATH_EQUAL = 167,
  MATH_MINUS = 164,
  MATH_MULTIPLY = 165,
  MATH_NOT_EQUAL = 168,
  MATH_PLUS = 163,
  MOON = 24,
  NON_ISOSCELES_TRAPEZOID = 143,
  NOTCHED_RIGHT_ARROW = 50,
  NO_SYMBOL = 19,
  OCTAGON = 6,
  OVAL = 9,
  OVAL_CALLOUT = 107,
  PARALLELOGRAM = 2,
  PENTAGON = 51,
  PIE = 142,
  PIE_WEDGE = 175,
  PLAQUE = 28,
  PLAQUE_TABS = 171,
  QUAD_ARROW = 39,
  QUAD_ARROW_CALLOUT = 59,
  RECTANGLE = 1,
  RECTANGULAR_CALLOUT = 105,
  REGULAR_PENTAGON = 12,
  RIGHT_ARROW = 33,
  RIGHT_ARROW_CALLOUT = 53,
  RIGHT_BRACE = 32,
  RIGHT_BRACKET = 30,
  RIGHT_TRIANGLE = 8,
  ROUNDED_RECTANGLE = 5,
  ROUNDED_RECTANGULAR_CALLOUT = 106,
  ROUND_1_RECTANGLE = 151,
  ROUND_2_DIAG_RECTANGLE = 153,
  ROUND_2_SAME_RECTANGLE = 152,
  SMILEY_FACE = 17,
  SNIP_1_RECTANGLE = 155,
  SNIP_2_DIAG_RECTANGLE = 157,
  SNIP_2_SAME_RECTANGLE = 156,
  SNIP_ROUND_RECTANGLE = 154,
  SQUARE_TABS = 170,
  STAR_10_POINT = 149,
  STAR_12_POINT = 150,
  STAR_16_POINT = 94,
  STAR_24_POINT = 95,
  STAR_32_POINT = 96,
  STAR_4_POINT = 91,
  STAR_5_POINT = 92,
  STAR_6_POINT = 147,
  STAR_7_POINT = 148,
  STAR_8_POINT = 93,
  STRIPED_RIGHT_ARROW = 49,
  SUN = 23,
  SWOOSH_ARROW = 178,
  TEAR = 160,
  TRAPEZOID = 3,
  UP_ARROW = 35,
  UP_ARROW_CALLOUT = 55,
  UP_DOWN_ARROW = 38,
  UP_DOWN_ARROW_CALLOUT = 58,
  UP_RIBBON = 97,
  U_TURN_ARROW = 42,
  VERTICAL_SCROLL = 101,
  WAVE = 103
}

export enum PptxConnectorType {
  CURVE = 3,
  ELBOW = 2,
  STRAIGHT = 1,
  MIXED = -2
}

export interface PptxSpacingModel {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface PptxPositionModel {
  left: number;
  top: number;
  width: number;
  height: number;
}

export interface PptxFontModel {
  name: string;
  size: number;
  font_weight: number;
  italic: boolean;
  color: string;
}

export interface PptxFillModel {
  color: string;
  opacity: number;
}

export interface PptxStrokeModel {
  color: string;
  thickness: number;
  opacity: number;
}

export interface PptxShadowModel {
  radius: number;
  offset: number;
  color: string;
  opacity: number;
  angle: number;
}

export interface PptxTextRunModel {
  text: string;
  font?: PptxFontModel;
}

export interface PptxParagraphModel {
  spacing?: PptxSpacingModel;
  alignment?: PptxAlignment;
  font?: PptxFontModel;
  line_height?: number;
  text?: string;
  text_runs?: PptxTextRunModel[];
}

export interface PptxObjectFitModel {
  fit?: PptxObjectFitEnum;
  focus?: [number | null, number | null];
}

export interface PptxPictureModel {
  is_network: boolean;
  path: string;
}

export interface PptxShapeModel {
}

export interface PptxTextBoxModel extends PptxShapeModel {
  margin?: PptxSpacingModel;
  fill?: PptxFillModel;
  position: PptxPositionModel;
  text_wrap: boolean;
  paragraphs: PptxParagraphModel[];
}

export interface PptxAutoShapeBoxModel extends PptxShapeModel {
  type?: PptxShapeType;
  margin?: PptxSpacingModel;
  fill?: PptxFillModel;
  stroke?: PptxStrokeModel;
  shadow?: PptxShadowModel;
  position: PptxPositionModel;
  text_wrap: boolean;
  border_radius?: number;
  paragraphs?: PptxParagraphModel[];
}

export interface PptxPictureBoxModel extends PptxShapeModel {
  position: PptxPositionModel;
  margin?: PptxSpacingModel;
  clip: boolean;
  opacity?: number;
  invert?: boolean;
  border_radius?: number[];
  shape?: PptxBoxShapeEnum;
  object_fit?: PptxObjectFitModel;
  picture: PptxPictureModel;
}

export interface PptxConnectorModel extends PptxShapeModel {
  type?: PptxConnectorType;
  position: PptxPositionModel;
  thickness: number;
  color: string;
  opacity: number;
}

export interface PptxSlideModel {
  background?: PptxFillModel;
  shapes: (PptxTextBoxModel | PptxAutoShapeBoxModel | PptxConnectorModel | PptxPictureBoxModel)[];
}

export interface PptxPresentationModel {
  name?: string;
  shapes?: PptxShapeModel[];
  slides: PptxSlideModel[];
}

export const createPptxSpacingAll = (num: number): PptxSpacingModel => ({
  top: num,
  left: num,
  bottom: num,
  right: num
});

export const createPptxPositionForTextbox = (left: number, top: number, width: number): PptxPositionModel => ({
  left,
  top,
  width,
  height: 100
});

export const positionToPtList = (position: PptxPositionModel): number[] => {
  return [position.left, position.top, position.width, position.height];
};

export const positionToPtXyxy = (position: PptxPositionModel): number[] => {
  const left = position.left;
  const top = position.top;
  const width = position.width;
  const height = position.height;

  return [left, top, left + width, top + height];
};
