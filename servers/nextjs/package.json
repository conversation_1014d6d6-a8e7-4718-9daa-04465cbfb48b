{"name": "presenton", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.2.8", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-underline": "^2.0.0", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "jsonrepair": "^3.12.0", "lucide-react": "^0.447.0", "marked": "^15.0.11", "mermaid": "^11.9.0", "next": "^14.2.14", "next-themes": "^0.4.6", "puppeteer": "^24.13.0", "react": "^18", "react-dom": "^18", "react-redux": "^9.1.2", "recharts": "^2.15.4", "sharp": "^0.34.3", "sonner": "^2.0.6", "tailwind-merge": "^2.5.3", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@types/animejs": "^3.1.12", "@types/node": "^20", "@types/puppeteer": "^5.4.7", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "cypress": "^14.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"brace-expansion": "2.0.2"}, "peerDependencies": {"zod": "^4.0.5"}}