#!/bin/bash

# Presenton 启动脚本
# 使用本地 Ollama 模型和 Pexels 图像服务

echo "正在启动 Presenton..."
echo "使用 Ollama 模型: qwen3:14b"
echo "图像提供商: Pexels (免费)"
echo ""

# 创建数据目录
mkdir -p ./app_data

# 启动 Presenton Docker 容器
docker run -it --name presenton \
  -p 5000:80 \
  -e LLM="ollama" \
  -e OLLAMA_URL="http://host.docker.internal:11434" \
  -e OLLAMA_MODEL="qwen3:14b" \
  -e IMAGE_PROVIDER="pexels" \
  -e PEXELS_API_KEY="YOUR_PEXELS_API_KEY_HERE" \
  -e CAN_CHANGE_KEYS="true" \
  -v "./app_data:/app_data" \
  ghcr.io/presenton/presenton:latest

echo ""
echo "Presenton 已启动！"
echo "请在浏览器中访问: http://localhost:5000"
